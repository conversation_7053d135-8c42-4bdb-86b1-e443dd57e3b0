{"_comment.k": "Key", "key.categories.otyacraftengine": "Otyacraft Engine", "key.otyacraftengine.test": "Test", "_comment.cop": "Components", "gui.narrate.widget": "%s widget", "gui.narrate.containerTab": "%s container tab", "narration.containerTab.usage.focused": "Press Enter to open and close details", "narration.containerTab.usage.hovered": "Left click to open and close details", "gui.narrate.radioButton": "%s radio button", "narration.radioButton.usage.focused": "Press Enter to select", "narration.radioButton.usage.hovered": "Left click to select", "gui.narrate.switchButton": "%s switch button", "narration.switchButton.usage.focused": "Press Enter to switch", "narration.switchButton.usage.hovered": "Left click to switch", "gui.narrate.fixedListWidget": "%s fixed list", "narration.fixedListWidget.usage.focused": "Press Enter to select", "narration.fixedListWidget.usage.hovered": "Left click to select", "_comment.con": "Config", "text.autoconfig.otyacraftengine.title": "Otyacraft Engine Config", "text.autoconfig.otyacraftengine.category.client": "Client", "text.autoconfig.otyacraftengine.category.client_debug": "Client Debug", "text.autoconfig.otyacraftengine.option.clientConfig.urlTextureConfig": "URL Texture", "text.autoconfig.otyacraftengine.option.clientConfig.urlTextureConfig.maxLoaderCount": "Max loader count", "text.autoconfig.otyacraftengine.option.clientConfig.urlTextureConfig.urlRegex": "Allow url regex", "text.autoconfig.otyacraftengine.option.debugConfig.showTagTooltip": "Show tag tooltip", "text.autoconfig.otyacraftengine.option.debugConfig.showModNameTooltip": "Show mod name tooltip", "text.autoconfig.otyacraftengine.option.debugConfig.showWidgetData": "Show widget data", "text.autoconfig.otyacraftengine.option.debugConfig.highlightVoxelShape": "Highlight voxel shape", "_comment.other": "Other", "modmenu.descriptionTranslation.otyacraftengine": "Integrated library used by felnull developers.\nThe ikisugi dependency mod...", "_comment.la": "Last"}