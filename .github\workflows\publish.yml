name: Publish
on:
  release:
  workflow_dispatch:
jobs:
  build:
    runs-on: blacksmith-4vcpu-ubuntu-2404
    permissions: 
      contents: read
      packages: write 
    steps:
      - uses: actions/checkout@v3
      - uses: useblacksmith/setup-java@v5
        with:
          java-version: '17'
          distribution: 'zulu'
      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@v3.5.0
      - name: Publish package
        uses: gradle/gradle-build-action@v3.5.0
        with:
          arguments: publish
        env:
          USERNAME: ${{ secrets.USERNAME }}
          MAVEN_PASSWORD: ${{ secrets.PASSWORD }}
