{"required": true, "package": "org.modsauce.otyacraftenginerenewed.fabric.mixin", "compatibilityLevel": "JAVA_17", "minVersion": "0.8", "plugin": "org.modsauce.otyacraftenginerenewed.fabric.mixin.OEMixinPluginFabric", "client": ["org.modsauce.otyacraftenginerenewed.fabric.mixin.client.ClientPacketListenerMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.client.FogRendererAccessor", "org.modsauce.otyacraftenginerenewed.fabric.mixin.client.FogRendererFogDataAccessor", "org.modsauce.otyacraftenginerenewed.fabric.mixin.client.FogRendererMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.client.ItemInHandRendererMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.client.MinecraftAccessor", "org.modsauce.otyacraftenginerenewed.fabric.mixin.client.MinecraftMixin"], "mixins": ["org.modsauce.otyacraftenginerenewed.fabric.mixin.EntityMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.LivingEntityMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.MobBucketItemAccessor", "org.modsauce.otyacraftenginerenewed.fabric.mixin.PoiTypesInvoker", "org.modsauce.otyacraftenginerenewed.fabric.mixin.data.FabricDataGenHelperMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.data.RegistriesDatapackGeneratorMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.data.RegistrySetBuilderAccessor", "org.modsauce.otyacraftenginerenewed.fabric.mixin.self.PlatformResourceReloadListenerMixin", "org.modsauce.otyacraftenginerenewed.fabric.mixin.self.StackAttributeModifierItemExtender"], "injectors": {"defaultRequire": 1}}