{"homepage": "https://www.curseforge.com/minecraft/mc-mods/otyacraft-engine", "1.19.2": {"3.0": "First 1.19.2"}, "1.18.2": {"2.4": "First 1.18.2"}, "1.18.1": {"2.3": "change widget", "2.2": "add code", "2.1": "First 1.18.1"}, "1.17.1": {"2.0": "First 1.17.1"}, "1.16.5": {"1.23": "Last 1.16.5 update..?"}, "promos": {"1.19.2-latest": "3.2.0", "1.19.2-recommended": "3.2.0", "1.18.2-latest": "2.13", "1.18.2-recommended": "2.13", "1.18.1-latest": "2.3", "1.18.1-recommended": "2.3", "1.17.1-latest": "2.0", "1.17.1-recommended": "2.0", "1.16.5-latest": "1.23", "1.16.5-recommended": "1.23", "1.19.3-latest": "3.5.0", "1.19.3-recommended": "3.5.0", "1.19.4-latest": "3.6.0", "1.19.4-recommended": "3.6.0", "1.20-latest": "3.7.0-alpha.2", "1.20-recommended": "3.7.0-alpha.2", "1.20.1-latest": "3.7.0-alpha.2", "1.20.1-recommended": "3.7.0-alpha.2"}, "1.19.3": {"3.3.0-alpha.1": "Changed\n- Port MC1.19.3\nRemoved\n- Separated <PERSON><PERSON><PERSON>'s built-in OBJ loader ([Special Model Loader](https://github.com/TeamFelnull/SpecialModelLoader))\n", "3.3.0-beta.1": "Changed\n- Port MC1.19.3\nRemoved\n- Separated Fabric's built-in OBJ loader ([Special Model Loader](https://github.com/TeamFelnull/SpecialModelLoader))\nFixed\n- Fixed a problem that crashes without notifying when Architecture is not installed (Forge Only)\n", "3.3.0": "Added\n- Add new utility class\n", "3.4.0": "Added\n- Add living tick event\n", "3.5.0": "Changed\n- Update CrossDataGenerator\n- Changes to rendering processing\n"}, "1.19.4": {"3.6.0-alpha.1": "Changed\n- Port 1.19.4\n", "3.6.0": "Changed\n- Fixed a bug in the URL texture loader\n"}, "1.20": {"3.7.0-alpha.2": "Fixed\n- Fixed wrong premise mod version\n"}, "1.20.1": {"3.7.0-alpha.2": "Fixed\n- Fixed wrong premise mod version\n"}}