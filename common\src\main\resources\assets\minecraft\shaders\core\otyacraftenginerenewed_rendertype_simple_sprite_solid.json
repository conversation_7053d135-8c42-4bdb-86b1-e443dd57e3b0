{"blend": {"func": "add", "srcrgb": "srcalpha", "dstrgb": "1-srcalpha"}, "vertex": "otyacraftengine_rendertype_simple_sprite_solid", "fragment": "otyacraftengine_rendertype_simple_sprite_solid", "attributes": ["Position", "Color", "UV0", "UV1", "UV2", "Normal"], "samplers": [{"name": "Sampler0"}], "uniforms": [{"name": "ModelViewMat", "type": "matrix4x4", "count": 16, "values": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "ProjMat", "type": "matrix4x4", "count": 16, "values": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "IViewRotMat", "type": "matrix3x3", "count": 9, "values": [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "ColorModulator", "type": "float", "count": 4, "values": [1.0, 1.0, 1.0, 1.0]}, {"name": "FogStart", "type": "float", "count": 1, "values": [0.0]}, {"name": "FogEnd", "type": "float", "count": 1, "values": [1.0]}, {"name": "FogColor", "type": "float", "count": 4, "values": [0.0, 0.0, 0.0, 0.0]}, {"name": "FogShape", "type": "int", "count": 1, "values": [0]}]}