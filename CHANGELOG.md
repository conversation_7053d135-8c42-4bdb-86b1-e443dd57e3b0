# Changelog
Changelog to track updates for this mod.  
    Add your changes to Unreleased if you want to commit.  
    Please write according to [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)

## [Unreleased]

### Added

### Changed

### Deprecated

### Removed

### Fixed

### Security

## [3.7.0-alpha.2] - 2023-07-01

### Fixed
- Fixed wrong premise mod version

## [3.7.0-alpha.1] - 2023-07-01

### Changed
- Port MC1.20

## [3.6.0] - 2023-03-27

### Changed
- Fixed a bug in the URL texture loader

## [3.6.0-alpha.1] - 2023-03-22

### Changed
- Port 1.19.4

## [3.5.0] - 2023-03-19

### Changed
- Update CrossDataGenerator
- Changes to rendering processing

## [3.4.0] - 2023-01-25

### Added
- Add living tick event

## [3.3.0] - 2023-01-24

### Added
- Add new utility class

## [3.3.0-beta.1] - 2023-01-22

### Changed
- Port MC1.19.3

### Removed
- Separated Fabric's built-in OBJ loader ([Special Model Loader](https://github.com/TeamFelnull/SpecialModelLoader))

### Fixed
- Fixed a problem that crashes without notifying when Architecture is not installed (Forge Only)

## [3.3.0-alpha.1] - 2023-01-18

### Changed
- Port MC1.19.3

### Removed
- Separated Fabric's built-in OBJ loader ([Special Model Loader](https://github.com/TeamFelnull/SpecialModelLoader))

[Unreleased]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.7.0-alpha.2...HEAD
[3.7.0-alpha.1]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.6.0...v3.7.0-alpha.1
[3.7.0-alpha.2]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.7.0-alpha.1...v3.7.0-alpha.2
[3.6.0]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.6.0-alpha.1...v3.6.0
[3.6.0-alpha.1]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.5.0...v3.6.0-alpha.1
[3.5.0]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.4.0...v3.5.0
[3.4.0]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.3.0...v3.4.0
[3.3.0]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.3.0-beta.1...v3.3.0
[3.3.0-beta.1]: https://github.com/TeamFelnull/OtyacraftEngine/compare/v3.3.0-alpha.1...v3.3.0-beta.1
[3.3.0-alpha.1]: https://github.com/TeamFelnull/OtyacraftEngine/commits/v3.3.0-alpha.1
