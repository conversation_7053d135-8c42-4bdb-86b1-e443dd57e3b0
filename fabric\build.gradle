plugins {
    id "com.github.johnrengelman.shadow" version "8.1.1"
    id 'com.matthewprenger.cursegradle' version '1.4.0'
    id "com.modrinth.minotaur" version "2.8.0"
}

architectury {
    platformSetupLoomIde()
    fabric()
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath
}

configurations {
    common
    shadowCommon 
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentFabric.extendsFrom common
    implementation.extendsFrom shadowIn
    shadowCommon.extendsFrom shadowIn
}

repositories {
    maven { url = "https://maven.terraformersmc.com/releases/" }//modmenu
    maven { url = "https://hephaestus.dev/release" }//myron
}

dependencies {
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"
    modApi "net.fabricmc.fabric-api:fabric-api:${rootProject.fabric_api_version}"
   
    modApi "dev.architectury:architectury-fabric:${rootProject.architectury_version}"

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionFabric")) { transitive false }

    modApi "com.terraformersmc:modmenu:${rootProject.modmenu_version}"
    modApi "me.shedaniel.cloth:cloth-config-fabric:${rootProject.cloth_config_version}"
    modRuntimeOnly "me.shedaniel:RoughlyEnoughItems-fabric:${rootProject.rei_version}"

    shadowIn "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    shadowIn 'com.madgag:animated-gif-lib:1.4'
}

processResources {
    inputs.property "version", project.version

    filesMatching("fabric.mod.json") {
        expand "version": project.version
    }
}

shadowJar {
    configurations = [project.configurations.shadowIn]
    relocate('dev.felnull.fnjl', 'org.modsauce.otyacraftenginerenewed.include.dev.felnull.fnjl')
    relocate 'com.madgag', 'org.modsauce.otyacraftenginerenewed.include.com.madgag'
}

shadowJar {
    exclude "architectury.common.json"

    configurations = [project.configurations.shadowCommon]
    archiveClassifier.set("org-shadow")
}

remapJar {
    injectAccessWidener = true
    input.set shadowJar.archiveFile
    dependsOn shadowJar
    archiveClassifier.set(null)
    setArchivesBaseName("${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}")
}

jar {
    archiveClassifier.set("org")
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}

publishing {
    repositories {
        maven {
            url repsyUrl
            credentials {
                username "${System.getenv("USERNAME")}"
                password "${System.getenv("MAVEN_PASSWORD")}"
            }
        }
    }
    publications {
        maven(MavenPublication) {
            from(components.java)
            groupId = "org.modsauce" // or whatever you want
            artifactId = "otyacraftenginerenewed-fabric" // fabric, forge, or nothing
            version = rootProject.version
        }
    }
}
/*
curseforge {
    if (System.getenv('curesforgeapikey') != null && "${project.curesforge_id}" != '') {
        apiKey = System.getenv('curesforgeapikey')
        project {
            id = "${rootProject.curesforge_id}"

            changelogType = 'markdown'
            changelog = file('../LATEST_CHANGELOG.md')

            releaseType = "${rootProject.release_type}"
            addGameVersion "${rootProject.minecraft_version}"
            addGameVersion 'Java 17'
            addGameVersion "Fabric"
            addGameVersion "Quilt"

            project.support_versions.split(",").each {
                String version -> addGameVersion version
            }

            relations {
                requiredDependency "fabric-api"
                requiredDependency "cloth-config"
                requiredDependency "architectury-api"
            }

            mainArtifact(file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}.jar")) {
                displayName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Fabric"
            }

            addArtifact(file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}-sources.jar")) {
                displayName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Fabric-sources"
            }
            afterEvaluate {
                uploadTask.dependsOn("build")
            }
        }
    }
    options {
        forgeGradleIntegration = false
    }
}

if (System.getenv('modrinthapikey') != null && "${modrinth_id}" != '') {
    modrinth {
        token = System.getenv('modrinthapikey')
        projectId = "${rootProject.modrinth_id}"
        versionName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Fabric"
        versionNumber = "${rootProject.minecraft_version}-${project.mod_version}-Fabric"
        versionType = "${project.release_type}"
        uploadFile = file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}.jar")
        gameVersions = List.of(project.support_versions.split(","))
        loaders = ["fabric", "quilt"]
        dependencies {
            required.project("P7dR8mSH")//Fabric API
            required.project("9s6osm5g")//Cloth Config API
            required.project("lhGA9TYQ")//Architectury API
        }
        changelog = file('../LATEST_CHANGELOG.md').text
    }
}*/
