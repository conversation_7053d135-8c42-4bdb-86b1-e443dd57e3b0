name: Mod Build
on: [ push ]
jobs:
  build:
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 200
      - name: Set up JDK 17
        uses: useblacksmith/setup-java@v5
        with:
          java-version: 17
          distribution: zulu
          cache: gradle
      - name: Lo<PERSON> Cache
        uses: useblacksmith/cache@v5
        with:
          path: "**/.gradle/loom-cache"
          key: "${{ runner.os }}-gradle-${{ hashFiles('**/libs.versions.*', '**/*.gradle*', '**/gradle-wrapper.properties') }}"
          restore-keys: "${{ runner.os }}-gradle-"
      - uses: gradle/actions/wrapper-validation@v4
      - run: chmod +x ./gradlew
      - name: Build with Gradle
        run: ./gradlew build
      - name: Upload Build Artifacts forge
        uses: actions/upload-artifact@v4
        with:
          name: OtaycraftEngineRenewedForge
          path: forge/build/libs
          compression-level: 9
      - name: Upload Build Artifacts fabric
        uses: actions/upload-artifact@v4
        with:
          name: OtaycraftEngineRenewedFabric
          path: fabric/build/libs
          compression-level: 9
        env:
          TOKEN: ${{ secrets.JAVA_TOKEN1 }}
