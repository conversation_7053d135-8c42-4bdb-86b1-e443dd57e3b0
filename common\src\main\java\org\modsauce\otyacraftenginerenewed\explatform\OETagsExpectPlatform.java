package org.modsauce.otyacraftenginerenewed.explatform;

import dev.architectury.injectables.annotations.ExpectPlatform;
import org.modsauce.otyacraftenginerenewed.tag.ManualTagHolder;
import net.minecraft.tags.TagKey;
import net.minecraft.world.item.Item;

import java.util.List;
import java.util.Optional;

public class OETagsExpectPlatform {
    @ExpectPlatform
    public static Optional<TagKey<Item>> pickaxes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static Optional<TagKey<Item>> shovels() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static Optional<TagKey<Item>> hoes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static Optional<TagKey<Item>> axes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> shears() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static Optional<TagKey<Item>> swords() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> bows() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> ironIngots() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> goldIngots() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> copperIngots() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> netheriteIngots() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> redstoneDusts() {
        throw new AssertionError();

    }

    @ExpectPlatform
    public static TagKey<Item> diamonds() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> glassBlocks() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> glassPanes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static TagKey<Item> books() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> ironNuggets() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> enderPearls() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> stone() {
        throw new AssertionError();

    }

    @ExpectPlatform
    public static ManualTagHolder<Item> redstoneBlocks() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> rawMeats() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> cookedMeats() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> rawFishes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> cookedFishes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> wheatBreads() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> breads() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> vegetables() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> carrots() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> potatoes() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> beetroots() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> wheatGrains() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> grains() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> seeds() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> fruits() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> milks() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> drinks() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> ironBlocks() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static List<ManualTagHolder<Item>> slimeBalls() {
        throw new AssertionError();
    }

    @ExpectPlatform
    public static ManualTagHolder<Item> clayBalls() {
        throw new AssertionError();
    }
}
