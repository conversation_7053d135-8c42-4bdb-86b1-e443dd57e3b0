modLoader = "javafml"
loaderVersion = "47.4.0"
issueTrackerURL = "https://github.com/Mod-Sauce/OtyacraftEngineRenewed/issues"
license = "GNU LGPLv3"

[[mods]]
modId = "otyacraftenginerenewed"
version = "${version}"
displayName = "Otyacraft Engine Renewed"
displayURL = "mod-sauce.github.io/oer.html"
updateJSONURL = ""
authors = "Shadowbe27, Mooo0042"
credits = "Mod Sauce & contributers"
description = '''
Integrated library used by felnull developers.
The ikisugi dependency mod...
'''
logoFile = "logo.png"

[modproperties.otyacraftengine]
catalogueImageIcon = "icon.png"

[[dependencies.otyacraftenginerenewed]]
modId = "forge"
mandatory = true
versionRange = "47.4.0"
ordering = "NONE"
side = "BOTH"

[[dependencies.otyacraftenginerenewed]]
modId = "minecraft"
mandatory = true
versionRange = "1.20.2"
ordering = "NONE"
side = "BOTH"

[[dependencies.otyacraftenginerenewed]]
modId = "architectury"
mandatory = true
versionRange = "10.1.20"
ordering = "AFTER"
side = "BOTH"

[[dependencies.otyacraftenginerenewed]]
modId = "cloth_config"
mandatory = true
versionRange = "12.0.137"
ordering = "NONE"
side = "BOTH"