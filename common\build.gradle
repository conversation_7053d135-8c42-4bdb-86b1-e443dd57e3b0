architectury {
    common(rootProject.enabled_platforms.split(","))
}

loom {
    accessWidenerPath = file("src/main/resources/otyacraftenginerenewed.accesswidener")
}

dependencies {
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"
    modApi "dev.architectury:architectury:${rootProject.architectury_version}"
    modApi "me.shedaniel.cloth:cloth-config:${rootProject.cloth_config_version}"
    implementation "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    implementation 'com.madgag:animated-gif-lib:1.4'
}

publishing {
    repositories {
        maven {
            url = repsyUrl
            credentials {
                username = "${System.getenv("USERNAME")}"
                password = "${System.getenv("MAVEN_PASSWORD")}"
            }
        }
    }
    publications {
        gpr(MavenPublication) {
            from(components.java)
            groupId = "org.modsauce" // or whatever you want
            artifactId = "otyacraftenginerenewed" // fabric, forge, or nothing
            version = rootProject.version
        }
    }
}
