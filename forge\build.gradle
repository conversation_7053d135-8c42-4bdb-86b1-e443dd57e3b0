plugins {
    id "com.github.johnrengelman.shadow" version "8.1.1"
    id 'com.matthewprenger.cursegradle' version '1.4.0'
    id "com.modrinth.minotaur" version "2.8.0"
}

architectury {
    platformSetupLoomIde()
    forge()
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath

    forge {
        convertAccessWideners = true
        extraAccessWideners.add loom.accessWidenerPath.get().asFile.name

        mixinConfig "otyacraftenginerenewed-common.mixins.json"
        mixinConfig "otyacraftenginerenewed.mixins.json"
    }
}

configurations {
    common
    shadowCommon
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentForge.extendsFrom common
    forgeDependencies.extendsFrom shadowIn
    shadowCommon.extendsFrom shadowIn
}

dependencies {
    forge "net.minecraftforge:forge:${rootProject.forge_version}"
    
    modApi "dev.architectury:architectury-forge:${rootProject.architectury_version}"

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionForge")) { transitive = false }

    modApi "me.shedaniel.cloth:cloth-config-forge:${rootProject.cloth_config_version}"
    modRuntimeOnly "me.shedaniel:RoughlyEnoughItems-forge:${rootProject.rei_version}"

    shadowIn "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    shadowIn 'com.madgag:animated-gif-lib:1.4'
}

processResources {
    inputs.property "version", project.version

    filesMatching("META-INF/mods.toml") {
        expand "version": project.version
    }
}

shadowJar {
    configurations = [project.configurations.shadowIn]
    relocate('dev.felnull.fnjl', 'org.modsauce.otyacraftenginerenewed.include.dev.felnull.fnjl')
    relocate 'com.madgag', 'org.modsauce.otyacraftenginerenewed.include.com.madgag'
}

shadowJar {
    exclude "fabric.mod.json"
    exclude "architectury.common.json"

    configurations = [project.configurations.shadowCommon]
    archiveClassifier.set("dev-shadow")
}

remapJar {
    input.set shadowJar.archiveFile
    dependsOn shadowJar
    archiveClassifier.set(null)
    setArchivesBaseName("${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}")
}

jar {
    archiveClassifier.set("org")
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}

publishing {
    repositories {
        maven {
            url repsyUrl
            credentials {
                username "${System.getenv("USERNAME")}"
                password "${System.getenv("MAVEN_PASSWORD")}"
            }
        }
    }
    publications {
        maven(MavenPublication) {
            from(components.java)
            groupId = "org.modsauce" // or whatever you want
            artifactId = "otyacraftenginerenewed-forge" // fabric, forge, or nothing
            version = rootProject.version
        }
    }
}
