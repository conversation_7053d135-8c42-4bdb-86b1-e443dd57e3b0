{"schemaVersion": 1, "id": "otyacraftenginerenewed", "version": "${version}", "name": "Otyacraft Engine Renewed", "description": "Integrated library used by felnull developers.\nThe ikisugi dependency mod...", "authors": ["Shadowbee27", "Mooo0042"], "contributors": ["Mod <PERSON> & contributors"], "contact": {"email": "<EMAIL>", "homepage": "mod-sauce.github.io/oer.html", "sources": "https://github.com/Mod-Sauce/OtyacraftEngineRenewed/", "issues": "https://github.com/Mod-Sauce/OtyacraftEngineRenewed/issues"}, "license": "GNU LGPLv3", "icon": "assets/otyacraftenginerenewed/icon.png", "environment": "*", "entrypoints": {"main": ["org.modsauce.otyacraftenginerenewed.fabric.OtyacraftEngineFabric"], "client": ["org.modsauce.otyacraftenginerenewed.fabric.client.OtyacraftEngineClientFabric"]}, "mixins": ["otyacraftenginerenewed.mixins.json", "otyacraftenginerenewed-common.mixins.json"], "depends": {"fabric": ">=0.16.14", "minecraft": "1.20.2", "architectury": ">=10.1.20", "cloth-config2": ">=12.0.137"}, "custom": {"modmenu": {"badges": ["library"]}}}