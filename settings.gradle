pluginManagement {
    repositories {
        maven { url = "https://maven.fabricmc.net/" }
        maven { url = "https://maven.architectury.dev/" }
        maven { url = "https://maven.minecraftforge.net/" }
        gradlePluginPortal()
    }
}

include("common")
include("fabric")
include("forge")

if (System.getenv("GITHUB_REF") == null) {
    include("testmod-common")
    include("testmod-fabric")
    include("testmod-forge")
}

rootProject.name = "OtyacraftEngine"
