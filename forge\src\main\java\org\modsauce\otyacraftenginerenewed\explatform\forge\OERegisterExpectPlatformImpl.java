package org.modsauce.otyacraftenginerenewed.explatform.forge;

import dev.architectury.registry.registries.RegistrySupplier;
import org.modsauce.otyacraftenginerenewed.blockentity.BlockEntityCreateSupplier;
import net.minecraft.tags.TagKey;
import net.minecraft.world.entity.ai.village.poi.PoiType;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.minecraftforge.common.ForgeTier;
import org.jetbrains.annotations.NotNull;

import java.util.function.Supplier;

public class OERegisterExpectPlatformImpl {
    public static <T extends BlockEntity> BlockEntityType<T> createBlockEntity(BlockEntityCreateSupplier<? extends T> supplier, Block... blocks) {
        BlockEntityType.Builder<T> builder = BlockEntityType.Builder.of(supplier::create, blocks);
        return builder.build(null);
    }

    public static void registerPoiTypeBlockStates(RegistrySupplier<PoiType> poiTypeRegistrySupplier) {

    }

    public static Tier createTier(int level, int uses, float speed, float attackDamageBonus, int enchantmentValue, @NotNull TagKey<Block> tag, @NotNull Supplier<Ingredient> repairIngredient) {
     //   TierSortingRegistry.registerTier()
        return new ForgeTier(level, uses, speed, attackDamageBonus, enchantmentValue, tag, repairIngredient);
    }
}
